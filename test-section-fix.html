<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>子章节修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .title {
            color: #333;
            margin-bottom: 10px;
        }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
        .code {
            background-color: #f1f3f4;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>子章节功能修复验证</h1>
        
        <div class="section">
            <h3 class="title">修复说明</h3>
            <p>之前的问题：添加子章节时创建了新的 TpContent，但没有更新现有章节的内容，导致重新加载时看不到新添加的子章节。</p>
            <p><strong>修复方案：</strong>将新的子章节内容追加到现有章节内容中，然后更新 TpContent。</p>
        </div>

        <div class="section">
            <h3 class="title">1. 修复前后对比</h3>
            <button class="button" onclick="showBeforeAfter()">查看修复对比</button>
            <div id="compare-log" class="log"></div>
        </div>

        <div class="section">
            <h3 class="title">2. 内容合并逻辑测试</h3>
            <button class="button" onclick="testContentMerging()">测试内容合并</button>
            <div id="merge-log" class="log"></div>
        </div>

        <div class="section">
            <h3 class="title">3. 解析逻辑测试</h3>
            <button class="button" onclick="testContentParsing()">测试内容解析</button>
            <div id="parse-log" class="log"></div>
        </div>

        <div class="section">
            <h3 class="title">4. 完整流程模拟</h3>
            <button class="button" onclick="simulateFullProcess()">模拟完整流程</button>
            <div id="full-log" class="log"></div>
        </div>
    </div>

    <script>
        // 模拟内容解析函数（基于实际代码）
        function parseContentToSections(contentData, chapterId) {
            if (!contentData || !contentData.content) {
                return [];
            }

            const content = contentData.content;
            const sections = [];

            if (typeof content === 'string') {
                // 按换行符分割内容，过滤空行
                const paragraphs = content.split('\n').filter(p => p.trim().length > 0);

                paragraphs.forEach((paragraph, index) => {
                    // 如果段落太短，跳过
                    if (paragraph.trim().length < 10) return;

                    // 检查是否是子章节标题格式 ### 标题 ###
                    const titleMatch = paragraph.match(/^###\s*(.+?)\s*###$/);
                    if (titleMatch) {
                        // 这是一个子章节标题，跳过（内容在下一段）
                        return;
                    }

                    // 生成子章节
                    const section = {
                        id: `${chapterId}_section_${index + 1}`,
                        title: paragraph.length > 50 ?
                            paragraph.substring(0, 50) + '...' :
                            paragraph,
                        content: paragraph,
                        duration: Math.ceil(paragraph.length / 10),
                        order: index + 1
                    };

                    sections.push(section);
                });
            }

            // 如果没有解析出子章节，创建一个默认的
            if (sections.length === 0) {
                sections.push({
                    id: `${chapterId}_section_1`,
                    title: '章节内容',
                    content: typeof content === 'string' ? content : '暂无详细内容',
                    duration: 30,
                    order: 1
                });
            }

            return sections;
        }

        function log(element, message, type = 'info') {
            const span = document.createElement('span');
            span.className = type;
            span.textContent = message + '\n';
            element.appendChild(span);
            element.scrollTop = element.scrollHeight;
        }

        function showBeforeAfter() {
            const logElement = document.getElementById('compare-log');
            logElement.innerHTML = '';

            log(logElement, '=== 修复前的逻辑 ===', 'error');
            log(logElement, '1. 用户输入子章节标题和内容', 'info');
            log(logElement, '2. 创建新的 TpContent 记录', 'info');
            log(logElement, '3. 调用 /tp/content POST 接口', 'info');
            log(logElement, '4. 添加到本地 sections 数组', 'info');
            log(logElement, '5. 问题：重新加载时，新内容丢失！', 'error');
            log(logElement, '   原因：新的 TpContent 与现有章节内容分离', 'error');

            log(logElement, '\n=== 修复后的逻辑 ===', 'success');
            log(logElement, '1. 用户输入子章节标题和内容', 'info');
            log(logElement, '2. 获取现有章节的 TpContent', 'info');
            log(logElement, '3. 将新内容追加到现有内容：', 'info');
            log(logElement, '   格式：\\n\\n### 标题 ###\\n内容', 'info');
            log(logElement, '4. 更新现有的 TpContent 记录', 'info');
            log(logElement, '5. 重新加载章节内容', 'info');
            log(logElement, '6. 解析更新后的内容为子章节', 'success');
            log(logElement, '7. 结果：子章节持久化保存！', 'success');
        }

        function testContentMerging() {
            const logElement = document.getElementById('merge-log');
            logElement.innerHTML = '';

            log(logElement, '测试内容合并逻辑...', 'info');

            // 模拟现有内容
            const existingContent = `这是第一章的主要内容。

### 1.1 基础概念 ###
这里介绍基础概念的详细内容。

### 1.2 核心原理 ###
这里解释核心原理。`;

            log(logElement, '现有内容:', 'info');
            log(logElement, existingContent, 'info');

            // 模拟新增子章节
            const newSectionTitle = '1.3 实践应用';
            const newSectionContent = '这里介绍实践应用的具体案例和方法。';
            const newSectionText = `\n\n### ${newSectionTitle} ###\n${newSectionContent}`;

            log(logElement, '\n新增子章节格式:', 'info');
            log(logElement, newSectionText, 'info');

            // 合并内容
            const mergedContent = existingContent + newSectionText;

            log(logElement, '\n合并后的完整内容:', 'success');
            log(logElement, mergedContent, 'success');

            // 测试解析
            const sections = parseContentToSections({ content: mergedContent }, 'chapter_1');
            log(logElement, `\n解析出的子章节数量: ${sections.length}`, 'success');
            sections.forEach((section, index) => {
                log(logElement, `子章节 ${index + 1}: ${section.title}`, 'success');
            });
        }

        function testContentParsing() {
            const logElement = document.getElementById('parse-log');
            logElement.innerHTML = '';

            log(logElement, '测试内容解析逻辑...', 'info');

            const testContent = `这是章节的主要介绍内容。

### 第一小节 ###
这是第一小节的详细内容，包含了重要的知识点。

### 第二小节 ###
这是第二小节的内容，进一步深入讲解。

### 第三小节 ###
这是第三小节，提供了实践案例。`;

            log(logElement, '测试内容:', 'info');
            log(logElement, testContent, 'info');

            const sections = parseContentToSections({ content: testContent }, 'test_chapter');

            log(logElement, `\n解析结果 - 共 ${sections.length} 个子章节:`, 'success');
            sections.forEach((section, index) => {
                log(logElement, `${index + 1}. ID: ${section.id}`, 'info');
                log(logElement, `   标题: ${section.title}`, 'info');
                log(logElement, `   内容: ${section.content.substring(0, 50)}...`, 'info');
                log(logElement, `   时长: ${section.duration}分钟`, 'info');
                log(logElement, '', 'info');
            });
        }

        function simulateFullProcess() {
            const logElement = document.getElementById('full-log');
            logElement.innerHTML = '';

            log(logElement, '模拟完整的添加子章节流程...', 'info');

            // 步骤1：模拟现有章节数据
            const chapter = {
                id: 123,
                title: '第一章 基础知识',
                _moduleData: {
                    content: {
                        id: 456,
                        content: '这是第一章的基础内容。\n\n### 1.1 概述 ###\n这里是概述部分。'
                    }
                }
            };

            log(logElement, '步骤1: 现有章节数据', 'info');
            log(logElement, `章节ID: ${chapter.id}`, 'info');
            log(logElement, `章节标题: ${chapter.title}`, 'info');
            log(logElement, `内容ID: ${chapter._moduleData.content.id}`, 'info');

            // 步骤2：用户输入
            const sectionTitle = '1.2 详细说明';
            const sectionContent = '这里是详细说明的内容，包含了更多的技术细节。';

            log(logElement, '\n步骤2: 用户输入', 'info');
            log(logElement, `子章节标题: ${sectionTitle}`, 'info');
            log(logElement, `子章节内容: ${sectionContent}`, 'info');

            // 步骤3：内容合并
            const existingContent = chapter._moduleData.content.content;
            const newSectionText = `\n\n### ${sectionTitle} ###\n${sectionContent}`;
            const updatedContent = existingContent + newSectionText;

            log(logElement, '\n步骤3: 内容合并', 'info');
            log(logElement, '合并后的内容:', 'info');
            log(logElement, updatedContent, 'info');

            // 步骤4：模拟API调用
            log(logElement, '\n步骤4: 模拟API调用', 'info');
            log(logElement, 'PUT /tp/content', 'info');
            log(logElement, '请求数据: { id: 456, content: "..." }', 'info');
            log(logElement, '响应: { success: true, error: false }', 'success');

            // 步骤5：重新解析
            log(logElement, '\n步骤5: 重新解析子章节', 'info');
            const sections = parseContentToSections({ content: updatedContent }, chapter.id);
            log(logElement, `解析出 ${sections.length} 个子章节:`, 'success');
            sections.forEach((section, index) => {
                log(logElement, `  ${index + 1}. ${section.title}`, 'success');
            });

            log(logElement, '\n✅ 完整流程模拟成功！', 'success');
            log(logElement, '新添加的子章节将在重新加载时正确显示。', 'success');
        }
    </script>
</body>
</html>
